from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Q
from fuzzywuzzy import process, fuzz
import re
import logging

logger = logging.getLogger(__name__)

from users.models import User
from invoices.models import Supplier, Invoice, InvoiceItem
from parts.models import Part, PartMapping, PartAlias, Labor
from invoices.utils import process_invoice_pdf
from invoices.validation import validate_invoice
from .pagination import StandardResultsSetPagination

from .serializers import (
    UserSerializer, SupplierSerializer, InvoiceSerializer,
    InvoiceItemSerializer, PartSerializer, PartMappingSerializer,
    PartAliasSerializer
)

class UserViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]

class SupplierViewSet(viewsets.ModelViewSet):
    queryset = Supplier.objects.all()
    serializer_class = SupplierSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = Supplier.objects.all()
        name = self.request.query_params.get('name', None)
        if name:
            queryset = queryset.filter(name__icontains=name)
        return queryset

    @action(detail=False, methods=['post'])
    def find_similar(self, request):
        """
        Find suppliers with similar names to the provided name
        """
        name = request.data.get('name')
        if not name:
            return Response(
                {"detail": "Supplier name is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get all suppliers
        suppliers = Supplier.objects.all()

        # Use fuzzy matching to find similar names
        from fuzzywuzzy import process

        # Get the top 10 matches
        matches = process.extractBests(
            name,
            [supplier.name for supplier in suppliers],
            score_cutoff=50,  # Minimum similarity score (0-100)
            limit=10  # Maximum number of matches to return
        )

        # Create a list of supplier objects with similarity scores
        similar_suppliers = []
        for match_name, score in matches:
            # Find the supplier object with this name
            supplier = next((s for s in suppliers if s.name == match_name), None)
            if supplier:
                similar_suppliers.append({
                    'id': supplier.id,
                    'name': supplier.name,
                    'gstin': supplier.gstin,
                    'address': supplier.address,
                    'similarity_score': score
                })

        return Response({
            'query': name,
            'results': similar_suppliers
        })

class InvoiceViewSet(viewsets.ModelViewSet):
    queryset = Invoice.objects.all().order_by('-invoice_date', '-id')
    serializer_class = InvoiceSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = StandardResultsSetPagination

    def get_queryset(self):
        queryset = Invoice.objects.all().order_by('-invoice_date', '-id')

        # Filter by status
        status = self.request.query_params.get('status', None)
        if status:
            queryset = queryset.filter(status=status)

        # Filter by supplier
        supplier = self.request.query_params.get('supplier', None)
        if supplier:
            queryset = queryset.filter(supplier__name__icontains=supplier)

        # Filter by invoice number
        invoice_number = self.request.query_params.get('invoice_number', None)
        if invoice_number:
            queryset = queryset.filter(invoice_number__icontains=invoice_number)

        # Filter by date range
        start_date = self.request.query_params.get('start_date', None)
        end_date = self.request.query_params.get('end_date', None)
        if start_date and end_date:
            queryset = queryset.filter(invoice_date__range=[start_date, end_date])

        # Filter by ERP update status
        erp_updated = self.request.query_params.get('erp_updated', None)
        if erp_updated is not None:
            is_erp_updated = erp_updated.lower() == 'true'
            queryset = queryset.filter(is_erp_updated=is_erp_updated)

        # Prefetch related items if requested
        expand = self.request.query_params.get('expand', '')
        if 'items' in expand:
            queryset = queryset.prefetch_related('items')

        return queryset

    def retrieve(self, request, *args, **kwargs):
        """
        Override retrieve to handle expanded items better and ensure pagination
        doesn't limit the number of items returned for a specific invoice.
        """
        instance = self.get_object()
        logger.info(f"Retrieving invoice {instance.id} with expand={request.query_params.get('expand')}")

        # Check if items expansion is requested
        expand = request.query_params.get('expand', '')
        if 'items' in expand:
            # Make sure all items are loaded for this invoice
            item_count = instance.items.count()
            logger.info(f"Invoice {instance.id} has {item_count} items in database")

            # Prefetch related items to avoid N+1 query issues
            if hasattr(instance, 'prefetch_related'):
                instance = instance.prefetch_related('items')

            serializer = self.get_serializer(instance, context={'expand_items': True})
        else:
            serializer = self.get_serializer(instance)

        return Response(serializer.data)

    def perform_create(self, serializer):
        # Set the uploaded_by field to the current user
        serializer.save(uploaded_by=self.request.user)

    def create(self, request, *args, **kwargs):
        """
        Override create to handle invoice number validation
        """
        # Check if invoice_number is provided
        data = request.data.copy()

        # If no invoice_number is provided or it's empty, generate a temporary one
        if 'invoice_number' not in data or not data.get('invoice_number'):
            # Generate a temporary invoice number based on timestamp
            import time
            temp_invoice_number = f"TEMP-{int(time.time())}"
            data['invoice_number'] = temp_invoice_number
            request._full_data = data  # Update the request data

            logger.info(f"No invoice number provided, using temporary number: {temp_invoice_number}")

        try:
            return super().create(request, *args, **kwargs)
        except Exception as e:
            # Check if it's a duplicate invoice number error
            if 'unique constraint' in str(e).lower() and 'invoice_number' in str(e).lower():
                return Response(
                    {"detail": "An invoice with this invoice number already exists. Please use a different invoice number."},
                    status=status.HTTP_400_BAD_REQUEST
                )
            # Re-raise other exceptions
            logger.error(f"Error creating invoice: {str(e)}")
            raise

    @action(detail=True, methods=['post'])
    def process(self, request, pk=None):
        """
        Process an invoice to extract items from the PDF
        """
        invoice = self.get_object()

        # Check if the invoice is already processed
        if invoice.status == 'completed':
            return Response(
                {"detail": "Invoice already processed."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get the extraction method from the request
        extraction_method = request.data.get('extraction_method')
        logger.info(f"Requested extraction method: {extraction_method}")

        if extraction_method:
            try:
                extraction_method = int(extraction_method)
                # Validate that the extraction method is valid
                if extraction_method not in [1, 2, 3]:
                    return Response(
                        {"detail": "Invalid extraction method. Must be 1 (AI-based), 2 (Table-based), or 3 (Text-based)."},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                # Set the extraction method on the invoice
                invoice.extraction_method = extraction_method
                invoice.save(update_fields=['extraction_method'])
                logger.info(f"Using extraction method {extraction_method} for invoice {invoice.id}")
            except (ValueError, TypeError):
                return Response(
                    {"detail": "Invalid extraction method format. Must be an integer."},
                    status=status.HTTP_400_BAD_REQUEST
                )
        else:
            logger.info(f"No extraction method specified, using default method {invoice.extraction_method}")

        # Process the invoice PDF
        try:
            success, message = process_invoice_pdf(invoice)

            if success:
                return Response(
                    {"detail": message},
                    status=status.HTTP_200_OK
                )
            else:
                return Response(
                    {"detail": f"Error processing invoice: {message}"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
        except Exception as e:
            # Check if this is a duplicate invoice number error
            if "UNIQUE constraint failed: invoices_invoice.invoice_number" in str(e):
                # Extract the invoice number from the error or try to get it from the invoice
                try:
                    # Try to get the invoice number that was being set
                    from invoices.utils import extract_metadata
                    pdf_path = invoice.pdf_file.path
                    metadata = extract_metadata(pdf_path)
                    duplicate_invoice_number = metadata.get("invoice_number", "Unknown")

                    # Find the existing invoice with this number
                    try:
                        existing_invoice = Invoice.objects.get(invoice_number=duplicate_invoice_number)
                        return Response(
                            {
                                "error_type": "duplicate_invoice",
                                "detail": f"This invoice ({duplicate_invoice_number}) has already been uploaded.",
                                "duplicate_invoice_number": duplicate_invoice_number,
                                "existing_invoice_id": existing_invoice.id,
                                "existing_invoice_date": existing_invoice.invoice_date.isoformat() if existing_invoice.invoice_date else None,
                                "existing_supplier": existing_invoice.supplier.name if existing_invoice.supplier else None
                            },
                            status=status.HTTP_409_CONFLICT
                        )
                    except Invoice.DoesNotExist:
                        # Fallback if we can't find the existing invoice
                        return Response(
                            {
                                "error_type": "duplicate_invoice",
                                "detail": f"This invoice ({duplicate_invoice_number}) has already been uploaded.",
                                "duplicate_invoice_number": duplicate_invoice_number
                            },
                            status=status.HTTP_409_CONFLICT
                        )
                except Exception as extract_error:
                    logger.error(f"Error extracting invoice number for duplicate check: {str(extract_error)}")
                    # Fallback to generic duplicate message
                    return Response(
                        {
                            "error_type": "duplicate_invoice",
                            "detail": "This invoice has already been uploaded. Please upload a new invoice, or delete the existing invoice and reupload it."
                        },
                        status=status.HTTP_409_CONFLICT
                    )
            else:
                # Re-raise other exceptions
                logger.error(f"Error processing invoice {invoice.id}: {str(e)}", exc_info=True)
                return Response(
                    {"detail": f"Error processing invoice: {str(e)}"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

    @action(detail=True, methods=['post'])
    def revalidate(self, request, pk=None):
        """
        Revalidate an invoice to update validation flags
        """
        invoice = self.get_object()

        try:
            # Reset validation flags and revalidate
            logger.info(f"Revalidating invoice {invoice.id}")
            validate_invoice(invoice)

            # Return the updated invoice with items included
            serializer = self.get_serializer(invoice, context={'expand_items': True})
            return Response(serializer.data, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"Error revalidating invoice {invoice.id}: {str(e)}", exc_info=True)
            return Response(
                {"detail": f"Error revalidating invoice: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class InvoiceItemViewSet(viewsets.ModelViewSet):
    queryset = InvoiceItem.objects.all()
    serializer_class = InvoiceItemSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = InvoiceItem.objects.all()

        # Filter by invoice
        invoice_id = self.request.query_params.get('invoice', None)
        if invoice_id:
            queryset = queryset.filter(invoice_id=invoice_id)

        # Filter by mapping status
        is_mapped = self.request.query_params.get('is_mapped', None)
        if is_mapped is not None:
            is_mapped = is_mapped.lower() == 'true'
            queryset = queryset.filter(is_mapped=is_mapped)

        return queryset

class PartViewSet(viewsets.ModelViewSet):
    queryset = Part.objects.all()
    serializer_class = PartSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = Part.objects.all()

        # Filter by part number
        part_number = self.request.query_params.get('part_number', None)
        if part_number:
            queryset = queryset.filter(part_number__icontains=part_number)

        # Filter by name or description
        search = self.request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) | Q(description__icontains=search)
            )

        return queryset

    @action(detail=False, methods=['post'])
    def fuzzy_match(self, request):
        """
        Find potential matches for a part description using enhanced fuzzy matching based on names only
        """
        description = request.data.get('description', '')
        if not description:
            return Response(
                {"detail": "Description is required."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if we're searching for labor items
        item_type = request.data.get('item_type', 'unknown')
        is_labor_search = item_type == 'labor'

        # Log the incoming request
        logger.info(f"Fuzzy match request for: {description}, item_type: {item_type}")

        # Preprocess the description
        processed_description = self._preprocess_description(description)

        # Get all parts and their aliases
        parts = Part.objects.all()
        part_names = [(part.id, part.name, part.part_number, 'part') for part in parts]

        # Add aliases to the search list
        aliases = PartAlias.objects.all()
        alias_names = [(alias.part.id, alias.alias, alias.part.part_number, 'part') for alias in aliases]

        # Combine all names to search against
        all_names = part_names + alias_names

        # If we're searching for labor items or if item_type is unknown, include labor items
        if is_labor_search or item_type == 'unknown':
            # Get all labor items
            labor_items = Labor.objects.all()
            labor_names = [(labor.id, labor.name, labor.code, 'labor') for labor in labor_items]

            # Add labor items to the search list
            all_names.extend(labor_names)

            logger.info(f"Including {len(labor_names)} labor items in search")

        # Prepare search corpus - only use names, not part/labor numbers
        search_corpus = [name for _, name, _, _ in all_names]

        # Prepare results list
        results = []

        # Strategy 1: Token sort ratio (good for word order differences)
        token_sort_matches = process.extract(
            processed_description,
            search_corpus,
            scorer=fuzz.token_sort_ratio,
            limit=5  # Increased from 3 to 5 to get more name-based matches
        )

        for match_name, score in token_sort_matches:
            if score >= 60:  # Only include if score is reasonable
                # Find all matching items (parts or labor) with this name
                matching_items = [(item_id, item_type) for item_id, name, _, item_type in all_names if name == match_name]
                for item_id, item_type in matching_items:
                    # Handle different item types
                    if item_type == 'part':
                        part = Part.objects.get(id=item_id)
                        if not any(r.get('id') == part.id and r.get('type') == 'part' for r in results):  # Avoid duplicates
                            results.append({
                                'id': part.id,
                                'part_number': part.part_number,
                                'name': part.name,
                                'score': score,
                                'match_type': 'token_sort',
                                'type': 'part'
                            })
                    elif item_type == 'labor':
                        labor = Labor.objects.get(id=item_id)
                        if not any(r.get('id') == labor.id and r.get('type') == 'labor' for r in results):  # Avoid duplicates
                            results.append({
                                'id': labor.id,
                                'part_number': labor.code,  # Use labor code as part_number
                                'name': labor.name,
                                'score': score,
                                'match_type': 'token_sort',
                                'type': 'labor'
                            })

        # Strategy 2: Token set ratio (good for partial matches)
        token_set_matches = process.extract(
            processed_description,
            search_corpus,
            scorer=fuzz.token_set_ratio,
            limit=5  # Increased from 3 to 5
        )

        for match_name, score in token_set_matches:
            if score >= 70:  # Higher threshold for token set
                # Find all matching items (parts or labor) with this name
                matching_items = [(item_id, item_type) for item_id, name, _, item_type in all_names if name == match_name]
                for item_id, item_type in matching_items:
                    # Handle different item types
                    if item_type == 'part':
                        part = Part.objects.get(id=item_id)
                        if not any(r.get('id') == part.id and r.get('type') == 'part' for r in results):  # Avoid duplicates
                            results.append({
                                'id': part.id,
                                'part_number': part.part_number,
                                'name': part.name,
                                'score': score,
                                'match_type': 'token_set',
                                'type': 'part'
                            })
                    elif item_type == 'labor':
                        labor = Labor.objects.get(id=item_id)
                        if not any(r.get('id') == labor.id and r.get('type') == 'labor' for r in results):  # Avoid duplicates
                            results.append({
                                'id': labor.id,
                                'part_number': labor.code,  # Use labor code as part_number
                                'name': labor.name,
                                'score': score,
                                'match_type': 'token_set',
                                'type': 'labor'
                            })

        # Strategy 3: Partial ratio (good for substring matches)
        partial_matches = process.extract(
            processed_description,
            search_corpus,
            scorer=fuzz.partial_ratio,
            limit=5  # Increased from 3 to 5
        )

        for match_name, score in partial_matches:
            if score >= 80:  # Higher threshold for partial matches
                # Find all matching items (parts or labor) with this name
                matching_items = [(item_id, item_type) for item_id, name, _, item_type in all_names if name == match_name]
                for item_id, item_type in matching_items:
                    # Handle different item types
                    if item_type == 'part':
                        part = Part.objects.get(id=item_id)
                        if not any(r.get('id') == part.id and r.get('type') == 'part' for r in results):  # Avoid duplicates
                            results.append({
                                'id': part.id,
                                'part_number': part.part_number,
                                'name': part.name,
                                'score': score,
                                'match_type': 'partial',
                                'type': 'part'
                            })
                    elif item_type == 'labor':
                        labor = Labor.objects.get(id=item_id)
                        if not any(r.get('id') == labor.id and r.get('type') == 'labor' for r in results):  # Avoid duplicates
                            results.append({
                                'id': labor.id,
                                'part_number': labor.code,  # Use labor code as part_number
                                'name': labor.name,
                                'score': score,
                                'match_type': 'partial',
                                'type': 'labor'
                            })

        # Sort results by score (descending)
        results = sorted(results, key=lambda x: x['score'], reverse=True)

        # Limit to top 10 results
        results = results[:10]

        # Log the results
        logger.info(f"Fuzzy match results: {len(results)} matches found")

        return Response(results)

    def _preprocess_description(self, description):
        """
        Preprocess the description to improve matching
        """
        # Convert to lowercase
        processed = description.lower()

        # Remove common words that don't help with matching
        stop_words = ['the', 'and', 'for', 'with', 'of', 'in', 'on', 'at', 'to']
        for word in stop_words:
            processed = processed.replace(f' {word} ', ' ')

        # Remove special characters
        processed = re.sub(r'[^\w\s]', ' ', processed)

        # Remove extra whitespace
        processed = re.sub(r'\s+', ' ', processed).strip()

        return processed

class PartMappingViewSet(viewsets.ModelViewSet):
    queryset = PartMapping.objects.all()
    serializer_class = PartMappingSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = PartMapping.objects.all()

        # Filter by invoice item
        invoice_item_id = self.request.query_params.get('invoice_item', None)
        if invoice_item_id:
            queryset = queryset.filter(invoice_item_id=invoice_item_id)

        # Filter by part
        part_id = self.request.query_params.get('part', None)
        if part_id:
            queryset = queryset.filter(part_id=part_id)

        # Filter by verification status
        is_verified = self.request.query_params.get('is_verified', None)
        if is_verified is not None:
            is_verified = is_verified.lower() == 'true'
            queryset = queryset.filter(is_verified=is_verified)

        return queryset

    def create(self, request, *args, **kwargs):
        """
        Override create to handle the case where a mapping already exists
        and to handle both part and labor mappings
        """
        # Check if a mapping already exists for this invoice item
        invoice_item_id = request.data.get('invoice_item')
        if not invoice_item_id:
            return Response(
                {"detail": "invoice_item is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get the item type from the request or determine it from the HSN code
        item_type = request.data.get('item_type')
        if not item_type:
            # Try to determine item type from HSN code
            invoice_item = InvoiceItem.objects.filter(id=invoice_item_id).first()
            if invoice_item and invoice_item.hsn_code:
                # HSN codes with decimals are spare parts, without are labor
                item_type = 'labor' if '.' not in invoice_item.hsn_code else 'part'
            else:
                item_type = 'part'  # Default to part if we can't determine

        # Get the part or labor ID
        part_id = request.data.get('part')
        if not part_id:
            return Response(
                {"detail": "part ID is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if a mapping already exists for this invoice item
        existing_mapping = PartMapping.objects.filter(invoice_item_id=invoice_item_id).first()
        if existing_mapping:
            # If update_existing is True, update the existing mapping
            if request.data.get('update_existing', False):
                # Update the existing mapping with the new part/labor and confidence score
                if item_type == 'part':
                    existing_mapping.mapping_type = 'part'
                    existing_mapping.part_id = part_id
                    existing_mapping.labor = None
                elif item_type == 'labor':
                    existing_mapping.mapping_type = 'labor'
                    existing_mapping.labor_id = part_id
                    existing_mapping.part = None

                existing_mapping.confidence_score = request.data.get('confidence_score', 0.0)
                existing_mapping.save()

                # Return the updated mapping
                serializer = self.get_serializer(existing_mapping)
                return Response(serializer.data)
            else:
                # Return an error with information about the existing mapping
                return Response(
                    {
                        "detail": "A mapping already exists for this invoice item. Set update_existing=true to update it.",
                        "existing_mapping": self.get_serializer(existing_mapping).data
                    },
                    status=status.HTTP_400_BAD_REQUEST
                )

        # If no existing mapping, create a new one
        data = request.data.copy()

        # Set the mapping type and appropriate fields
        if item_type == 'labor':
            data['mapping_type'] = 'labor'
            data['labor'] = part_id
            data.pop('part', None)  # Remove part if it exists
        else:
            data['mapping_type'] = 'part'
            # part_id is already in the data

        # Create serializer with modified data
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    @action(detail=False, methods=['post'])
    def find_by_description(self, request):
        """
        Find existing mappings for a given description
        """
        description = request.data.get('description')
        if not description:
            return Response(
                {"detail": "Description is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Find invoice items with exact matching descriptions that have been mapped
        from invoices.models import InvoiceItem
        from django.db.models import Q

        # First try exact match
        matching_items = InvoiceItem.objects.filter(
            Q(description__iexact=description) & Q(is_mapped=True)
        ).select_related('mapping', 'mapping__part').order_by('-mapping__confidence_score')

        if not matching_items.exists():
            # If no exact match, try fuzzy match
            # Normalize the description for better matching
            normalized_description = description.lower().strip()

            # Find items where the description contains the search term or vice versa
            matching_items = InvoiceItem.objects.filter(
                Q(description__icontains=normalized_description) & Q(is_mapped=True)
            ).select_related('mapping', 'mapping__part').order_by('-mapping__confidence_score')

            # If still no matches, try token-based matching
            if not matching_items.exists():
                # Split the description into tokens and search for items containing these tokens
                tokens = set(normalized_description.split())
                if tokens:
                    q_objects = Q()
                    for token in tokens:
                        if len(token) > 3:  # Only use tokens with more than 3 characters
                            q_objects |= Q(description__icontains=token)

                    matching_items = InvoiceItem.objects.filter(
                        q_objects & Q(is_mapped=True)
                    ).select_related('mapping', 'mapping__part').order_by('-mapping__confidence_score')

        # If we found matching items, return the mapping information
        if matching_items.exists():
            # Get the most confident mapping
            best_match = matching_items.first()

            # Calculate a confidence score based on string similarity
            from fuzzywuzzy import fuzz
            similarity_score = fuzz.ratio(description.lower(), best_match.description.lower()) / 100.0

            # Return the mapping information
            return Response({
                "found": True,
                "part_id": best_match.mapping.part_id,
                "part_number": best_match.mapping.part.part_number,
                "part_name": best_match.mapping.part.name,
                "original_description": best_match.description,
                "confidence_score": similarity_score,
                "mapping_id": best_match.mapping.id
            })

        # If no matching items found
        return Response({
            "found": False,
            "detail": "No existing mappings found for this description"
        })

class PartAliasViewSet(viewsets.ModelViewSet):
    queryset = PartAlias.objects.all()
    serializer_class = PartAliasSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = PartAlias.objects.all()

        # Filter by part
        part_id = self.request.query_params.get('part', None)
        if part_id:
            queryset = queryset.filter(part_id=part_id)

        return queryset
