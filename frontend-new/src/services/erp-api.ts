import { Invoice } from './api';
import api from './api'; // Use the main API instance for proxy calls

// We'll use our backend proxy to avoid CORS issues
// Note: The api instance already prepends '/api/' to all requests, so we don't include it here
const ERP_PROXY_ENDPOINT = 'erp/proxy/';

// Interface for ERP API request body
export interface ERPRequestBody {
  Division: string;
  Office: string;
  TransactionDate: string;
  TransactionNo: string;
  Supplier: string;
  StateOfSupply: string;
  GSTNNo: string;
  UERRefNo: string;
  InvoiceDate: string;
  ITCApplicable: string;
  VendorBillNo: string;
  Vehicle: string;
  ExpenseVehicle: string;
  Type: string;
  JobSheetNo: string;
  IncidentNo: string;
  ORMNo: string;
  LabourExpenseAcc: string;
  SpareExpenseAcc: string;
  TCSAcc: string;
  DiscAcc: string;
  Labdata: LabourItem[];
  Sparedata: SpareItem[];
}

export interface LabourItem {
  VehicleNo: string;
  TrailorNo: string;
  JobsheetNo: string;
  LabourCode: string;
  HSNCode: string;
  Qty: number;
  Rate: number;
  Amount: number;
  GSTP: number;
  SGST: number;
  CGST: number;
  IGST: number;
  TotalAmount: number;
  DoneBy: string;
  TrailorExp: string;
  ComplaintReason: string;
  Remarks: string;
  DiscountAmount: number | string;
}

export interface SpareItem {
  VehicleNo: string;
  TrailorNo: string;
  JobsheetNo: string;
  SpareCode: string;
  SpareMake: string;
  HSNCode: string;
  Qty: number;
  Rate: number;
  Amount: number;
  GSTP: number;
  SGST: number;
  CGST: number;
  IGST: number;
  TotalAmount: number;
  DoneBy: string;
  TrailorExp: string;
  ComplaintReason: string;
  Remarks: string;
  DiscountAmount: number | string;
}

// Helper function to check if an item has 100% discount
const hasFullDiscount = (item: any): boolean => {
  // For debugging
  const itemDesc = item.description || item.mapped_name || 'Unknown item';

  // Check if discount_percentage is explicitly set to 100 or 100.00
  const discountPercentage = item.discount_percentage;

  // Handle numeric values
  if (discountPercentage === 100 || discountPercentage === 100.00) {
    console.log(`Item "${itemDesc}" has explicit 100% discount (numeric): ${discountPercentage}`);
    return true;
  }

  // Handle string values
  if (typeof discountPercentage === 'string') {
    // Remove any % sign and trim whitespace
    const cleanDiscount = discountPercentage.replace('%', '').trim();
    if (cleanDiscount === '100' || cleanDiscount === '100.00' || cleanDiscount === '100.0') {
      console.log(`Item "${itemDesc}" has explicit 100% discount (string): ${discountPercentage}`);
      return true;
    }
  }

  // Check if total_price is 0 but base_amount is positive (implicit 100% discount)
  const baseAmount = item.base_amount || (item.quantity * item.unit_price);
  const totalPrice = item.total_price || 0;

  const isFullDiscount = totalPrice === 0 && baseAmount > 0;

  if (isFullDiscount) {
    console.log(`Item "${itemDesc}" has implicit 100% discount (total: ${totalPrice}, base: ${baseAmount})`);
  }

  return isFullDiscount;
};

// Helper function to calculate tax amounts from invoice data
const calculateTaxAmounts = (item: any) => {
  const baseAmount = item.base_amount || (item.quantity * item.unit_price);
  const totalPrice = item.total_price || 0;

  // Calculate total tax amount from the difference between total and base
  const totalTaxAmount = totalPrice - baseAmount;

  // Determine tax distribution based on percentages
  const igstPercentage = item.igst_percentage || 0;
  const cgstPercentage = item.cgst_percentage || 0;
  const sgstPercentage = item.sgst_percentage || 0;

  let igstAmount = 0;
  let cgstAmount = 0;
  let sgstAmount = 0;

  if (igstPercentage > 0) {
    // Interstate transaction - all tax is IGST
    igstAmount = totalTaxAmount;
  } else if (cgstPercentage > 0 || sgstPercentage > 0) {
    // Intrastate transaction - split between CGST and SGST
    // Usually they are equal, but use the actual percentages if available
    const totalIntrastateTaxPercentage = cgstPercentage + sgstPercentage;
    if (totalIntrastateTaxPercentage > 0) {
      cgstAmount = (totalTaxAmount * cgstPercentage) / totalIntrastateTaxPercentage;
      sgstAmount = (totalTaxAmount * sgstPercentage) / totalIntrastateTaxPercentage;
    } else {
      // Fallback: split equally
      cgstAmount = totalTaxAmount / 2;
      sgstAmount = totalTaxAmount / 2;
    }
  }

  // Round to 2 decimal places
  return {
    igstAmount: Math.round(igstAmount * 100) / 100,
    cgstAmount: Math.round(cgstAmount * 100) / 100,
    sgstAmount: Math.round(sgstAmount * 100) / 100
  };
};

// Function to format invoice data for ERP API
export const formatInvoiceForERP = (invoice: Invoice): ERPRequestBody => {
  // Extract vehicle number from the invoice
  const vehicleNo = invoice.vehicle_number || '';

  // Format transaction date (invoice date)
  const transactionDate = new Date(invoice.invoice_date).toISOString().split('T')[0];

  // Initialize labor and spare arrays
  const labourItems: LabourItem[] = [];
  const spareItems: SpareItem[] = [];

  // Process invoice items
  invoice.items.forEach(item => {
    // Log the item's discount percentage for debugging
    console.log(`Processing item "${item.description}" with discount: ${item.discount_percentage} (${typeof item.discount_percentage})`);

    if (item.is_mapped) {
      // Calculate tax amounts from invoice data
      const taxAmounts = calculateTaxAmounts(item);

      // Determine if it's a labor or spare part based on mapping
      const mappingType = item.mapping_type || 'part'; // Default to part if not specified

      if (mappingType === 'labor') {
        // Add to labor items
        labourItems.push({
          VehicleNo: vehicleNo,
          TrailorNo: '',
          JobsheetNo: '',
          LabourCode: `${item.mapped_name || item.description}`,
          HSNCode: item.hsn_code || '',
          // The ERP API expects Qty to be a number, not a string
          Qty: Number(item.quantity),
          Rate: item.unit_price,
          Amount: item.base_amount || (item.quantity * item.unit_price),
          GSTP: (item.igst_percentage || 0) + (item.cgst_percentage || 0) + (item.sgst_percentage || 0),
          SGST: taxAmounts.sgstAmount,
          CGST: taxAmounts.cgstAmount,
          IGST: taxAmounts.igstAmount,
          TotalAmount: item.total_price || (item.quantity * item.unit_price * (1 + ((item.igst_percentage || 0) + (item.cgst_percentage || 0) + (item.sgst_percentage || 0))/100)),
          DoneBy: '',
          TrailorExp: 'N',
          ComplaintReason: '',
          Remarks: '',
          DiscountAmount: hasFullDiscount(item) ? 100 : (item.discount_percentage || 0)
        });
      } else {
        // Add to spare parts
        spareItems.push({
          VehicleNo: vehicleNo,
          TrailorNo: '',
          JobsheetNo: '',
          SpareCode: `${item.mapped_name || item.description}`,
          SpareMake: '',
          HSNCode: item.hsn_code || '',
          // The ERP API expects Qty to be a number, not a string
          Qty: Number(item.quantity),
          Rate: item.unit_price,
          Amount: item.base_amount || (item.quantity * item.unit_price),
          GSTP: (item.igst_percentage || 0) + (item.cgst_percentage || 0) + (item.sgst_percentage || 0),
          SGST: taxAmounts.sgstAmount,
          CGST: taxAmounts.cgstAmount,
          IGST: taxAmounts.igstAmount,
          TotalAmount: item.total_price || (item.quantity * item.unit_price * (1 + ((item.igst_percentage || 0) + (item.cgst_percentage || 0) + (item.sgst_percentage || 0))/100)),
          DoneBy: '',
          TrailorExp: 'N',
          ComplaintReason: '',
          Remarks: '',
          DiscountAmount: hasFullDiscount(item) ? 100 : (item.discount_percentage || 0)
        });
      }
    }
  });

  // Create the full request body
  return {
    Division: "Transportation",
    Office: "BHANKROTA-JPR",
    TransactionDate: transactionDate,
    TransactionNo: invoice.invoice_number,
    Supplier: invoice.supplier_name,
    StateOfSupply: invoice.state || '',
    GSTNNo: invoice.dealer_gstin || '',
    UERRefNo: '',
    InvoiceDate: transactionDate,
    ITCApplicable: 'Y',
    VendorBillNo: invoice.invoice_number,
    Vehicle: vehicleNo,
    ExpenseVehicle: vehicleNo,
    Type: 'General',
    JobSheetNo: '',
    IncidentNo: '',
    ORMNo: '',
    LabourExpenseAcc: "Repair Truck Expense A/c",
    SpareExpenseAcc: "Repair Truck Expense A/c",
    TCSAcc: '',
    DiscAcc: '',
    Labdata: labourItems,
    Sparedata: spareItems
  };
};

// Service for ERP API operations
export const erpService = {
  // Test if the ERP proxy endpoint is working
  testProxyEndpoint: async () => {
    try {
      console.log('Testing ERP proxy endpoint...');
      const response = await api.get(ERP_PROXY_ENDPOINT);
      console.log('ERP proxy test response:', response.data);
      return response;
    } catch (error) {
      console.error('Error testing ERP proxy endpoint:', error);
      throw error;
    }
  },

  // Send invoice data to ERP
  sendInvoiceToERP: async (invoice: Invoice) => {
    try {
      console.log('Sending invoice to ERP:', invoice.invoice_number);

      // Format the invoice data for ERP
      const erpData = formatInvoiceForERP(invoice);
      console.log('Formatted ERP data:', erpData);

      // Log the request body for debugging
      console.log('ERP request body:', JSON.stringify(erpData, null, 2));

      // Log specific fields for verification
      console.log('ERP request details:');
      console.log('- Invoice Number:', erpData.TransactionNo);
      console.log('- Vehicle:', erpData.Vehicle);
      console.log('- Supplier:', erpData.Supplier);
      console.log('- GSTN:', erpData.GSTNNo);
      console.log('- Labor Items:', erpData.Labdata.length);
      console.log('- Spare Items:', erpData.Sparedata.length);

      // Log the first labor item if available
      if (erpData.Labdata.length > 0) {
        console.log('First Labor Item:', JSON.stringify(erpData.Labdata[0], null, 2));
        console.log('Labor Item Discount:', erpData.Labdata[0].DiscountAmount);
        console.log('Labor Item Qty (value):', erpData.Labdata[0].Qty);
        console.log('Labor Item Qty (type):', typeof erpData.Labdata[0].Qty);
      }

      // Log the first spare item if available
      if (erpData.Sparedata.length > 0) {
        console.log('First Spare Item:', JSON.stringify(erpData.Sparedata[0], null, 2));
        console.log('Spare Item Discount:', erpData.Sparedata[0].DiscountAmount);
        console.log('Spare Item Qty (value):', erpData.Sparedata[0].Qty);
        console.log('Spare Item Qty (type):', typeof erpData.Sparedata[0].Qty);
      }

      // Log items with 100% discount
      const laborItemsWithFullDiscount = erpData.Labdata.filter(item => item.DiscountAmount === 100);
      const spareItemsWithFullDiscount = erpData.Sparedata.filter(item => item.DiscountAmount === 100);

      console.log(`Labor items with 100% discount: ${laborItemsWithFullDiscount.length}`);
      console.log(`Spare items with 100% discount: ${spareItemsWithFullDiscount.length}`);

      // Log all discount values for debugging
      console.log('All labor item discounts:', erpData.Labdata.map(item => ({
        description: item.LabourCode,
        discount: item.DiscountAmount
      })));

      console.log('All spare item discounts:', erpData.Sparedata.map(item => ({
        description: item.SpareCode,
        discount: item.DiscountAmount
      })));

      // Get the CSRF token directly from cookies
      const getCsrfToken = () => {
        const name = 'csrftoken=';
        const decodedCookie = decodeURIComponent(document.cookie);
        const cookieArray = decodedCookie.split(';');

        for (let i = 0; i < cookieArray.length; i++) {
          let cookie = cookieArray[i].trim();
          if (cookie.indexOf(name) === 0) {
            return cookie.substring(name.length, cookie.length);
          }
        }
        return null;
      };

      // Get the CSRF token
      const csrfToken = getCsrfToken();

      // Send the request through our backend proxy with explicit CSRF token
      const response = await api.post(ERP_PROXY_ENDPOINT, erpData, {
        params: {
          endpoint: 'POSTData'
        },
        headers: {
          'X-CSRFToken': csrfToken || ''
        }
      });

      console.log('ERP API response:', response.data);

      return response;
    } catch (error) {
      console.error('Error sending invoice to ERP:', error);
      throw error;
    }
  }
};

export default erpService;
