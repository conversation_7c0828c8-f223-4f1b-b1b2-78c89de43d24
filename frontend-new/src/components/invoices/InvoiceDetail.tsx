import { useState, useEffect, useMemo, useRef } from 'react';
import { motion } from 'framer-motion';
import { invoiceService } from '../../services/api';
import type { Invoice, InvoiceItem } from '../../services/api';
import InvoiceItemRow from './InvoiceItemRow';
import ClickableSupplier from '../suppliers/ClickableSupplier';
import MappingProgressBar from './MappingProgressBar';
import UpdateERPButton from './UpdateERPButton';
import ErpRequestDataViewer from './ErpRequestDataViewer';
import { getMediaUrl } from '../../utils/url';

interface InvoiceDetailProps {
  invoiceId: number;
  onBack?: () => void;
  onInvoiceProcessed?: () => void;
}

const InvoiceDetail = ({ invoiceId, onBack, onInvoiceProcessed }: InvoiceDetailProps) => {
  const [invoice, setInvoice] = useState<Invoice | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [processing, setProcessing] = useState(false);
  const [processingSuccess, setProcessingSuccess] = useState(false);
  const [validating, setValidating] = useState(false);
  const [validationSuccess, setValidationSuccess] = useState(false);
  const [erpUpdateSuccess, setErpUpdateSuccess] = useState(false);
  const [erpUpdateError, setErpUpdateError] = useState<string | null>(null);
  const [selectedItemForMapping, setSelectedItemForMapping] = useState<InvoiceItem | null>(null);
  const [duplicateInvoiceDialog, setDuplicateInvoiceDialog] = useState<{
    show: boolean;
    invoiceNumber: string;
    existingInvoiceId?: number;
    existingInvoiceDate?: string;
    existingSupplier?: string;
  }>({
    show: false,
    invoiceNumber: '',
  });
  const itemRowRefs = useRef<Record<number, HTMLElement | null>>({});

  useEffect(() => {
    fetchInvoiceDetails();
    // Reset processing states when invoice ID changes
    setProcessing(false);
    setProcessingSuccess(false);
  }, [invoiceId]);

  const fetchInvoiceDetails = async () => {
    try {
      setLoading(true);
      const response = await invoiceService.getInvoice(invoiceId);
      console.log(`Loaded invoice with ${response.data.items?.length || 0} items`);

      // Sort items by item_number to ensure correct order
      if (response.data.items) {
        response.data.items.sort((a, b) => a.item_number - b.item_number);

        // Check if we're missing items
        if (response.data.items.length < 8) {
          console.warn(`Warning: Expected 8 items but only loaded ${response.data.items.length}. Check backend extraction.`);
        }
      }

      setInvoice(response.data);
      setError(null);
    } catch (err: any) {
      console.error('Error fetching invoice details:', err);
      setError(err.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const reprocessInvoice = async () => {
    if (!invoice) return;

    try {
      setProcessing(true);
      setError(null);
      setProcessingSuccess(false);

      const response = await invoiceService.processInvoice(invoice.id);
      console.log('Invoice reprocessing response:', response);

      // Fetch updated invoice details
      await fetchInvoiceDetails();

      // Show success message briefly
      setProcessingSuccess(true);

      // Call the callback to notify parent component about processing completion
      if (onInvoiceProcessed) {
        onInvoiceProcessed();
      }

      // Clear success message after a delay
      setTimeout(() => {
        setProcessingSuccess(false);
      }, 3000);

    } catch (err: any) {
      console.error('Error reprocessing invoice:', err);

      // Check if this is a duplicate invoice error
      if (err.response?.status === 409 && err.response?.data?.error_type === 'duplicate_invoice') {
        const errorData = err.response.data;
        setDuplicateInvoiceDialog({
          show: true,
          invoiceNumber: errorData.duplicate_invoice_number || 'Unknown',
          existingInvoiceId: errorData.existing_invoice_id,
          existingInvoiceDate: errorData.existing_invoice_date,
          existingSupplier: errorData.existing_supplier
        });
      } else {
        setError(err.response?.data?.detail || 'Failed to reprocess invoice');
      }
    } finally {
      setProcessing(false);
    }
  };

  // Function to revalidate the invoice
  const revalidateInvoice = async () => {
    if (!invoice) return;

    try {
      setValidating(true);
      setError(null);
      setValidationSuccess(false);

      // Call the revalidate API endpoint
      const response = await invoiceService.revalidateInvoice(invoice.id);
      console.log('Invoice revalidation response:', response);

      // Make sure the response data has all the required fields
      if (response.data && response.data.items) {
        // Update the invoice with the response data
        setInvoice(response.data);

        // Show success message briefly
        setValidationSuccess(true);

        // Clear success message after a delay
        setTimeout(() => {
          setValidationSuccess(false);
        }, 3000);

        // Call the callback to notify parent component about the update
        if (onInvoiceProcessed) {
          onInvoiceProcessed();
        }
      } else {
        // If the response doesn't have items, fetch the full invoice details
        console.log('Response missing items array, fetching full invoice details');
        await fetchInvoiceDetails();
        setValidationSuccess(true);

        setTimeout(() => {
          setValidationSuccess(false);
        }, 3000);
      }

    } catch (err: any) {
      console.error('Error revalidating invoice:', err);
      setError(err.response?.data?.detail || 'Failed to revalidate invoice');
    } finally {
      setValidating(false);
    }
  };

  // Function to open the invoice PDF in a new tab
  const openInvoicePdf = () => {
    if (invoice) {
      console.log('PDF File:', invoice.pdf_file);
      console.log('File URL:', invoice.file_url);

      // Use the utility function to get the correct URL
      let pdfUrl = '';

      if (invoice.file_url) {
        pdfUrl = getMediaUrl(invoice.file_url);
      } else if (invoice.pdf_file) {
        pdfUrl = getMediaUrl(invoice.pdf_file);
      }

      console.log('Using media URL:', pdfUrl);

      console.log('Opening PDF URL:', pdfUrl);

      // Create a hidden anchor element and trigger a click
      if (pdfUrl) {
        const link = document.createElement('a');
        link.href = pdfUrl;
        link.target = '_blank';
        link.rel = 'noopener noreferrer';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    }
  };

  // Function to update the supplier for the invoice
  const updateSupplier = async (supplierId: number) => {
    if (!invoice) return;

    try {
      // Update the invoice with the new supplier
      await invoiceService.updateInvoice(invoice.id, { supplier: supplierId });

      // Refresh the invoice details
      await fetchInvoiceDetails();

      return Promise.resolve();
    } catch (err: any) {
      console.error('Error updating supplier:', err);
      return Promise.reject(err);
    }
  };

  // Handle successful ERP update
  const handleErpUpdateSuccess = () => {
    setErpUpdateSuccess(true);
    setErpUpdateError(null);

    // Clear success message after a delay
    setTimeout(() => {
      setErpUpdateSuccess(false);
    }, 5000);
  };

  // Handle ERP update error
  const handleErpUpdateError = (errorMessage: string) => {
    setErpUpdateError(errorMessage);
    setErpUpdateSuccess(false);

    // Clear error message after a delay
    setTimeout(() => {
      setErpUpdateError(null);
    }, 5000);
  };

  // Function to handle mapping an item from the progress bar
  const handleMapItem = (item: InvoiceItem) => {
    setSelectedItemForMapping(item);

    // Find the corresponding row and scroll to it
    setTimeout(async () => {
      const rowElement = document.getElementById(`invoice-item-row-${item.id}`);
      if (rowElement) {
        rowElement.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // Highlight the row briefly
        rowElement.classList.add('bg-yellow-100');
        setTimeout(() => {
          rowElement.classList.remove('bg-yellow-100');
        }, 2000);

        // Find the InvoiceItemRow component instance for this item
        const itemRowComponent = invoice?.items.find(i => i.id === item.id);

        // Force expand the mapping panel for this item
        // We'll do this by adding a special attribute to the row
        rowElement.setAttribute('data-force-expand', 'true');

        // Simulate a click on the row to expand it
        const clickEvent = new MouseEvent('click', {
          bubbles: true,
          cancelable: true,
          view: window
        });
        rowElement.dispatchEvent(clickEvent);

        // If the panel doesn't expand after the click, we'll try a different approach
        // by directly accessing the mapping button if it exists
        setTimeout(() => {
          // Check if the panel is expanded by looking for the mapping panel
          const mappingPanel = rowElement.nextElementSibling?.querySelector('.mapping-panel');
          if (!mappingPanel) {
            // If not expanded, try to find and click the "Map" button in the description column
            const mapButton = rowElement.querySelector('.map-item-button');
            if (mapButton) {
              (mapButton as HTMLElement).click();
            }
          }
        }, 300);
      }
    }, 100);
  };

  // Function to handle mapping the supplier from the progress bar
  const handleMapSupplier = () => {
    // Find the supplier element and scroll to it
    const supplierElement = document.getElementById('invoice-supplier');
    if (supplierElement) {
      supplierElement.scrollIntoView({ behavior: 'smooth', block: 'center' });

      // Highlight the supplier element briefly
      supplierElement.classList.add('bg-yellow-100');
      setTimeout(() => {
        supplierElement.classList.remove('bg-yellow-100');
      }, 2000);

      // Simulate a click on the supplier element
      const clickEvent = new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
        view: window
      });
      supplierElement.dispatchEvent(clickEvent);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount);
  };

  // Calculate total of all invoice items and compare with grand total
  const totalValidation = useMemo(() => {
    if (!invoice || !invoice.items || invoice.items.length === 0 || !invoice.grand_total) {
      return null;
    }

    // Sum all item totals
    const itemsTotal = invoice.items.reduce((sum, item) => sum + (item.total_price || 0), 0);

    // Round to 2 decimal places to avoid floating point comparison issues
    const roundedItemsTotal = Math.round(itemsTotal * 100) / 100;
    const roundedGrandTotal = Math.round(invoice.grand_total * 100) / 100;

    // Calculate the difference
    const difference = Math.abs(roundedGrandTotal - roundedItemsTotal);

    // Check if the totals match (with a tolerance of Rs. 2.0 as requested)
    const isMatch = difference <= 2.0;

    return {
      isMatch,
      itemsTotal: roundedItemsTotal,
      invoiceGrandTotal: roundedGrandTotal,
      difference
    };
  }, [invoice]);

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (error) {
    return <div className="text-red-500 py-4">{error}</div>;
  }

  if (!invoice) {
    return <div className="text-gray-500 py-4">Invoice not found</div>;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="flex justify-between items-center mb-6">
        <button
          className="text-primary-600 hover:text-primary-800 flex items-center"
          onClick={onBack}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          Back to Invoices
        </button>
        <span className={`inline-block px-3 py-1 text-sm rounded-full ${getStatusColor(invoice.status)}`}>
          {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
        </span>
      </div>

      {/* Mapping Progress Bar */}
      {invoice && (
        <MappingProgressBar
          invoice={invoice}
          onMapItem={handleMapItem}
          onMapSupplier={handleMapSupplier}
        />
      )}

      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-800 mb-4">Invoice #{invoice.invoice_number}</h2>
            <div className="space-y-2">
              <p><span className="font-medium">Date:</span> {new Date(invoice.invoice_date).toLocaleDateString()}</p>
              <p className="flex items-center">
                <span className="font-medium mr-2">Supplier:</span>
                <div id="invoice-supplier">
                  <ClickableSupplier
                    supplier={{
                      id: invoice.supplier,
                      name: invoice.supplier_name || 'Unknown',
                      gstin: invoice.dealer_gstin
                    }}
                    onUpdateSupplier={updateSupplier}
                  />
                </div>
              </p>
              {invoice.customer && <p><span className="font-medium">Customer:</span> {invoice.customer}</p>}
              {invoice.vehicle_number && <p><span className="font-medium">Vehicle Number:</span> {invoice.vehicle_number}</p>}
              {invoice.dealer_gstin && <p><span className="font-medium">Dealer GSTIN:</span> {invoice.dealer_gstin}</p>}
              {invoice.state && <p><span className="font-medium">State:</span> {invoice.state}</p>}
            </div>
          </div>
          <div className="flex flex-col justify-between">
            <div className="text-right">
              <p className="text-sm text-gray-500">Uploaded by {invoice.uploaded_by_username}</p>
              <p className="text-sm text-gray-500">on {new Date(invoice.created_at).toLocaleString()}</p>
            </div>
            {invoice.grand_total && (
              <div className="mt-4 text-right">
                <p className="text-sm text-gray-500">Grand Total</p>
                <p className="text-2xl font-bold text-primary-600">{formatCurrency(invoice.grand_total)}</p>
              </div>
            )}
          </div>
        </div>

        {/* Backend validation status */}
        {invoice.has_validation_errors && (
          <div className="mt-6 border-t pt-4">
            <div
              className="backdrop-blur-sm bg-opacity-20 rounded-lg p-4 shadow-lg border bg-yellow-50 border-yellow-200 text-yellow-800"
              style={{
                backdropFilter: 'blur(8px)',
                WebkitBackdropFilter: 'blur(8px)'
              }}
            >
              <div className="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 mt-0.5 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <div>
                  <p className="font-medium">This invoice has validation errors in the database, but appears valid in the current view.</p>
                  <p className="mt-1">
                    {invoice.row_validation_error && <span className="font-medium">Row calculation errors were previously detected. </span>}
                    {invoice.total_validation_error && <span className="font-medium">Total mismatch of {formatCurrency(invoice.total_validation_error)} was previously detected. </span>}
                    <span>Use the "Revalidate" button to update the validation status based on the current invoice data.</span>
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Total validation check */}
        {totalValidation && (
          <div className="mt-6 border-t pt-4">
            <div
              className={`
                backdrop-blur-sm bg-opacity-20 rounded-lg p-4 shadow-lg border
                ${totalValidation.isMatch
                  ? 'bg-green-50 border-green-200 text-green-800'
                  : 'bg-yellow-50 border-yellow-200 text-yellow-800'}
              `}
              style={{
                backdropFilter: 'blur(8px)',
                WebkitBackdropFilter: 'blur(8px)'
              }}
            >
              {totalValidation.isMatch ? (
                <div className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <div>
                    <p className="font-medium">Item totals match the grand total from invoice.</p>
                    {totalValidation.difference > 0.01 && (
                      <p className="text-sm mt-1">
                        <span className="font-medium">Small difference of {formatCurrency(totalValidation.difference)} is acceptable.</span>
                        <span className="ml-2">(Sum of item totals: {formatCurrency(totalValidation.itemsTotal)} vs Invoice grand total: {formatCurrency(totalValidation.invoiceGrandTotal)})</span>
                      </p>
                    )}
                  </div>
                </div>
              ) : (
                <div className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 mt-0.5 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  <div>
                    <p className="font-medium">Some problem! Item totals don't match grand total from invoice.</p>
                    <p className="mt-1">
                      <span className="font-medium">Sum of item totals:</span> {formatCurrency(totalValidation.itemsTotal)} |
                      <span className="font-medium ml-2">Invoice grand total:</span> {formatCurrency(totalValidation.invoiceGrandTotal)} |
                      <span className="font-medium ml-2">Difference:</span> {formatCurrency(totalValidation.difference)}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Document actions and reprocess buttons */}
        <div className="mt-6 border-t pt-4">
          <div className="flex flex-col sm:flex-row justify-between items-center">
            <div>
              <span className="text-sm text-gray-500">
                {invoice.items.length < 8 && "Some invoice items may be missing. "}
                Use enhanced extraction to improve data quality.
              </span>
            </div>
            <div className="mt-3 sm:mt-0 flex space-x-3">
              <button
                onClick={openInvoicePdf}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                Open Invoice PDF
              </button>
              <button
                onClick={reprocessInvoice}
                disabled={processing}
                className={`inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white
                  ${processing ? 'bg-gray-500 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'}`}
              >
                {processing ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing...
                  </>
                ) : (
                  'Reprocess Invoice'
                )}
              </button>
              <button
                onClick={revalidateInvoice}
                disabled={validating}
                className={`inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white
                  ${validating ? 'bg-gray-500 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700'}`}
                title="Update validation status to match current invoice data"
              >
                {validating ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Validating...
                  </>
                ) : (
                  <>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Revalidate
                  </>
                )}
              </button>

              {/* ERP Update Button */}
              {invoice && (
                <UpdateERPButton
                  invoice={invoice}
                  onSuccess={handleErpUpdateSuccess}
                  onError={handleErpUpdateError}
                />
              )}
            </div>
          </div>

          {processingSuccess && (
            <div className="mt-3 bg-green-100 border border-green-400 text-green-700 px-4 py-2 rounded">
              Invoice successfully reprocessed. Data has been updated.
            </div>
          )}

          {validationSuccess && (
            <div className={`mt-3 ${invoice?.has_validation_errors ? 'bg-yellow-100 border-yellow-400 text-yellow-700' : 'bg-green-100 border-green-400 text-green-700'} border px-4 py-2 rounded`}>
              {invoice?.has_validation_errors
                ? 'Invoice validation status has been updated. Validation errors are still present.'
                : 'Invoice validation status has been updated. No validation errors found.'}
            </div>
          )}

          {/* ERP Update Success Message */}
          {erpUpdateSuccess && (
            <div className="mt-3 bg-green-100 border border-green-400 text-green-700 px-4 py-2 rounded">
              Invoice successfully updated in ERP system.
            </div>
          )}

          {/* ERP Update Error Message */}
          {erpUpdateError && (
            <div className="mt-3 bg-red-100 border border-red-400 text-red-700 px-4 py-2 rounded">
              Error updating ERP system: {erpUpdateError}
            </div>
          )}
        </div>
      </div>

      {/* ERP Request Data Viewer */}
      {invoice.is_erp_updated && (
        <ErpRequestDataViewer invoice={invoice} />
      )}

      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-xl font-semibold mb-4">Invoice Items</h3>

        {invoice.items.length === 0 ? (
          <p className="text-gray-500">No items found in this invoice.</p>
        ) : (
          <div className="overflow-x-auto rounded-lg border border-gray-200 shadow-sm">
            <table className="min-w-full divide-y divide-gray-200 table-fixed">
              <thead>
                <tr className="bg-gradient-to-r from-gray-50 to-gray-100">
                  <th scope="col" className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider w-[8%] border-b">
                    Item No
                  </th>
                  <th scope="col" className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider w-[12%] border-b">
                    HSN Code
                  </th>
                  <th scope="col" className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider w-[20%] border-b">
                    Description
                  </th>
                  <th scope="col" className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider w-[8%] border-b">
                    Quantity
                  </th>
                  <th scope="col" className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider w-[8%] border-b">
                    Unit Price
                  </th>
                  <th scope="col" className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider w-[8%] border-b">
                    Base Amt
                  </th>
                  <th scope="col" className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider w-[6%] border-b">
                    Disc %
                  </th>
                  <th scope="col" className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider w-[6%] border-b">
                    CGST %
                  </th>
                  <th scope="col" className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider w-[6%] border-b">
                    SGST %
                  </th>
                  <th scope="col" className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider w-[6%] border-b">
                    IGST %
                  </th>
                  <th scope="col" className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider w-[8%] border-b">
                    Total Price
                  </th>
                  <th scope="col" className="relative px-4 py-3 w-[6%] border-b">
                    <span className="sr-only">Status</span>
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {invoice.items.map((item) => (
                  <InvoiceItemRow
                    key={item.id}
                    item={item}
                    onMappingUpdated={fetchInvoiceDetails}
                  />
                ))}
              </tbody>
              <tfoot>
                <tr className="bg-gray-50">
                  <td colSpan={10} className="px-4 py-3 text-right text-sm font-medium text-gray-900 border-t">
                    Grand Total:
                  </td>
                  <td className="px-4 py-3 text-sm font-bold text-gray-900 border-t">
                    {formatCurrency(invoice.grand_total || 0)}
                  </td>
                  <td className="border-t"></td>
                </tr>
              </tfoot>
            </table>
          </div>
        )}
      </div>

      {/* Duplicate Invoice Dialog */}
      {duplicateInvoiceDialog.show && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-yellow-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <h3 className="text-lg font-medium text-gray-900">Duplicate Invoice Detected</h3>
            </div>

            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-3">
                This invoice <strong>{duplicateInvoiceDialog.invoiceNumber}</strong> has already been uploaded to the system.
              </p>

              {duplicateInvoiceDialog.existingInvoiceId && (
                <div className="bg-gray-50 rounded-md p-3 mb-3">
                  <p className="text-sm text-gray-700">
                    <strong>Existing Invoice Details:</strong>
                  </p>
                  <ul className="text-sm text-gray-600 mt-1 space-y-1">
                    <li>• Invoice ID: {duplicateInvoiceDialog.existingInvoiceId}</li>
                    {duplicateInvoiceDialog.existingInvoiceDate && (
                      <li>• Date: {new Date(duplicateInvoiceDialog.existingInvoiceDate).toLocaleDateString()}</li>
                    )}
                    {duplicateInvoiceDialog.existingSupplier && (
                      <li>• Supplier: {duplicateInvoiceDialog.existingSupplier}</li>
                    )}
                  </ul>
                </div>
              )}

              <p className="text-sm text-gray-600">
                Please upload a new invoice, or delete the existing invoice and reupload it if needed.
              </p>
            </div>

            <div className="flex justify-end space-x-3">
              {duplicateInvoiceDialog.existingInvoiceId && (
                <button
                  onClick={() => {
                    setDuplicateInvoiceDialog({ show: false, invoiceNumber: '' });
                    // Navigate to the existing invoice
                    window.location.href = `/invoices/${duplicateInvoiceDialog.existingInvoiceId}`;
                  }}
                  className="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100"
                >
                  View Existing Invoice
                </button>
              )}
              <button
                onClick={() => setDuplicateInvoiceDialog({ show: false, invoiceNumber: '' })}
                className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700"
              >
                OK
              </button>
            </div>
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default InvoiceDetail;
