import { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { invoiceService } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

interface InvoiceUploadProps {
  onUploadSuccess?: (invoice: any) => void;
  onUploadError?: (error: string) => void;
}

const InvoiceUpload = ({ onUploadSuccess, onUploadError }: InvoiceUploadProps) => {
  const { user, isAuthenticated } = useAuth();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [fileInfo, setFileInfo] = useState<{ name: string; size: string } | null>(null);
  const [uploading, setUploading] = useState(false);
  const [uploadSuccess, setUploadSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uploadStatusMessage, setUploadStatusMessage] = useState<string>('');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [duplicateInvoiceDialog, setDuplicateInvoiceDialog] = useState<{
    show: boolean;
    invoiceNumber: string;
    existingInvoiceId?: number;
    existingInvoiceDate?: string;
    existingSupplier?: string;
  }>({
    show: false,
    invoiceNumber: '',
  });

  useEffect(() => {
    console.log('InvoiceUpload component - Auth state:', { user, isAuthenticated });
  }, [user, isAuthenticated]);

  useEffect(() => {
    console.log('User state changed:', { user, isAuthenticated });
  }, [user, isAuthenticated]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // Check if file is a PDF
      if (file.type !== 'application/pdf') {
        setError('Please select a PDF file');
        return;
      }

      setSelectedFile(file);

      // Format file size
      const fileSizeKB = file.size / 1024;
      const fileSizeMB = fileSizeKB / 1024;
      const sizeDisplay = fileSizeMB >= 1
        ? `${fileSizeMB.toFixed(2)} MB`
        : `${fileSizeKB.toFixed(2)} KB`;

      setFileInfo({ name: file.name, size: sizeDisplay });
      setError(null);
    }
  };



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedFile) {
      setError('Please select a PDF file to upload');
      return;
    }

    try {
      setUploading(true);
      setError(null);

      const formData = new FormData();
      formData.append('pdf_file', selectedFile);

      // Add default values for required fields
      // We no longer set a default invoice_number as it should be extracted from the PDF
      const today = new Date().toISOString().split('T')[0]; // Format: YYYY-MM-DD
      formData.append('invoice_date', today);

      // Default supplier - can be replaced by the backend
      formData.append('supplier', '1');

      setUploadStatusMessage('Uploading invoice...');
      const response = await invoiceService.uploadInvoice(formData);

      // Process the invoice with text-based extraction (Method 3)
      setUploadStatusMessage('Processing invoice with Text-based extraction...');

      // Always use extraction method 3 (Text-based)
      await invoiceService.processInvoice(response.data.id, 3);

      setTimeout(() => {
        setUploadStatusMessage('');
        setUploadSuccess(true);

        if (onUploadSuccess) {
          onUploadSuccess(response.data);
        }
      }, 3000);

      setSelectedFile(null);
      setFileInfo(null);

      // Clear the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (err: any) {
      console.error('Error uploading invoice:', err);

      // Check if this is a duplicate invoice error
      if (err.response?.status === 409 && err.response?.data?.error_type === 'duplicate_invoice') {
        const errorData = err.response.data;
        setDuplicateInvoiceDialog({
          show: true,
          invoiceNumber: errorData.duplicate_invoice_number || 'Unknown',
          existingInvoiceId: errorData.existing_invoice_id,
          existingInvoiceDate: errorData.existing_invoice_date,
          existingSupplier: errorData.existing_supplier
        });
        setUploadStatusMessage('');
      } else {
        // Check for different types of error responses
        if (err.response?.data?.detail) {
          setError(err.response.data.detail);
        } else if (err.response?.data?.invoice_number) {
          // Handle specific invoice_number validation errors
          setError(`Invoice number error: ${err.response.data.invoice_number}`);
        } else if (typeof err.response?.data === 'object' && Object.keys(err.response.data).length > 0) {
          // Handle other field-specific errors
          const errorField = Object.keys(err.response.data)[0];
          const errorMessage = err.response.data[errorField];
          setError(`${errorField}: ${errorMessage}`);
        } else {
          setError('Failed to upload invoice. Please try again.');
        }
        setUploadStatusMessage('');

        if (onUploadError) {
          onUploadError(err.response?.data?.detail || 'Failed to upload invoice');
        }
      }
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-semibold mb-4">Upload New Invoice</h2>

      {!isAuthenticated ? (
        <div className="text-amber-600 py-2">Please log in to upload invoices.</div>
      ) : (
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label htmlFor="invoice-file" className="block text-sm font-medium text-gray-700 mb-1">
              Invoice PDF
            </label>
            <input
              ref={fileInputRef}
              type="file"
              id="invoice-file"
              accept=".pdf"
              onChange={handleFileChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              disabled={uploading}
            />
            {fileInfo && (
              <div className="mt-2 text-sm text-gray-600">
                <span className="font-medium">{fileInfo.name}</span> ({fileInfo.size})
              </div>
            )}
          </div>

          <div className="mb-4">
            <div className="text-sm text-gray-600">
              Invoices will be processed using Text-based extraction, which provides the best results for complex invoice formats.
            </div>
          </div>

          {error && (
            <div className="mb-4 p-3 bg-red-100 text-red-800 rounded-md">
              {error}
            </div>
          )}

          {uploadStatusMessage && (
            <div className="mb-4 p-3 bg-blue-100 text-blue-800 rounded-md">
              {uploadStatusMessage}
            </div>
          )}

          {uploadSuccess && (
            <div className="mb-4 p-3 bg-green-100 text-green-800 rounded-md">
              Invoice uploaded and processed successfully!
            </div>
          )}

          <div className="flex justify-end mt-6">
            <motion.button
              type="submit"
              className={`px-4 py-2 rounded-md text-white transition-colors ${
                !selectedFile || uploading
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-primary-600 hover:bg-primary-700'
              }`}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              disabled={!selectedFile || uploading}
            >
              {uploading ? (
                <div className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Uploading...
                </div>
              ) : (
                'Upload Invoice'
              )}
            </motion.button>
          </div>
        </form>
      )}

      {/* Duplicate Invoice Dialog */}
      {duplicateInvoiceDialog.show && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-yellow-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <h3 className="text-lg font-medium text-gray-900">Duplicate Invoice Detected</h3>
            </div>

            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-3">
                This invoice <strong>{duplicateInvoiceDialog.invoiceNumber}</strong> has already been uploaded to the system.
              </p>

              {duplicateInvoiceDialog.existingInvoiceId && (
                <div className="bg-gray-50 rounded-md p-3 mb-3">
                  <p className="text-sm text-gray-700">
                    <strong>Existing Invoice Details:</strong>
                  </p>
                  <ul className="text-sm text-gray-600 mt-1 space-y-1">
                    <li>• Invoice ID: {duplicateInvoiceDialog.existingInvoiceId}</li>
                    {duplicateInvoiceDialog.existingInvoiceDate && (
                      <li>• Date: {new Date(duplicateInvoiceDialog.existingInvoiceDate).toLocaleDateString()}</li>
                    )}
                    {duplicateInvoiceDialog.existingSupplier && (
                      <li>• Supplier: {duplicateInvoiceDialog.existingSupplier}</li>
                    )}
                  </ul>
                </div>
              )}

              <p className="text-sm text-gray-600">
                Please upload a new invoice, or delete the existing invoice and reupload it if needed.
              </p>
            </div>

            <div className="flex justify-end space-x-3">
              {duplicateInvoiceDialog.existingInvoiceId && (
                <button
                  onClick={() => {
                    setDuplicateInvoiceDialog({ show: false, invoiceNumber: '' });
                    // Navigate to the existing invoice
                    window.location.href = `/invoices/${duplicateInvoiceDialog.existingInvoiceId}`;
                  }}
                  className="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100"
                >
                  View Existing Invoice
                </button>
              )}
              <button
                onClick={() => setDuplicateInvoiceDialog({ show: false, invoiceNumber: '' })}
                className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700"
              >
                OK
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default InvoiceUpload;
